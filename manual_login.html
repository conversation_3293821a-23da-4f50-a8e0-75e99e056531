<!DOCTYPE html>
<html>
<head>
    <title>Manual Login</title>
</head>
<body>
    <h1>Manual Login</h1>
    <button onclick="performLogin()">Login as ADMIN001</button>
    <div id="status"></div>

    <script>
        async function performLogin() {
            const statusDiv = document.getElementById('status');
            statusDiv.innerHTML = 'Logging in...';
            
            try {
                // Make the login API call
                const response = await fetch('/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        employee_number: 'ADMIN001',
                        password: 'admin123'
                    })
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                console.log('Login response:', data);
                
                // Store tokens in localStorage
                localStorage.setItem('supplyline_access_token', data.access_token);
                localStorage.setItem('supplyline_refresh_token', data.refresh_token);
                localStorage.setItem('supplyline_user_data', JSON.stringify(data.user));
                
                statusDiv.innerHTML = 'Login successful! Redirecting to dashboard...';
                
                // Redirect to dashboard
                setTimeout(() => {
                    window.location.href = '/dashboard';
                }, 1000);
                
            } catch (error) {
                console.error('Login failed:', error);
                statusDiv.innerHTML = `Login failed: ${error.message}`;
            }
        }
    </script>
</body>
</html>
